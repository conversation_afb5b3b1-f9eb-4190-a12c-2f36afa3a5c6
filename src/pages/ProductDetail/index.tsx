import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useLocation, useHistory } from 'ice';
import classNames from 'classnames';
import Header from './Header';
import Detail from './Detail';
import Tutorial from './Tutorial';
import PureDoc from './PureDoc';
import NotFound from '@/help-fe-common/components/common/NotFound';
import NoteTip from '@/help-fe-common/components/common/ProductDetail/NoteTip';
import Outline from '@/help-fe-common/components/pc/Outline';
import FeedbackButton from '@/help-fe-common/components/common/Feedback/FeedbackButton';
import RecommendDoc from '@/help-fe-common/components/common/ProductDetail/RecommendDoc';
import useOutlineClick from '@/help-fe-common/hooks/useOutlineClick';
import useChangeTitle from '@/help-fe-common/hooks/useChangeTitle';
import { TutorialProcessor } from '@/utils/helpDoc/tutorialProcessor/tutorial';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { triggerUrlBehavior } from '@/help-fe-common/utils/global/url/urlBehavior';
import { isSameAlias } from '@/help-fe-common/utils/global/isSameAlias';
import contentProcessor from '@/help-fe-common/plugins/docDetailProcessor';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { getCssNumber } from '@/help-fe-common/utils/global/style/cssValue';
import { parseQuery } from '@/help-fe-common/utils/global/url/parseQuery';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { unescapeHtmlTag } from '@/help-fe-common/utils/helpDoc/escapeHtmlTag';
import { DetailModel } from '@/help-fe-common/models/DetailModel';
import { extractSynopsisData } from '@/help-fe-common/utils/helpDoc/docOutline';
import { storeDataMerge } from '@/help-fe-common/models/common/storeDataMerge';
import { detailStore } from '@/help-fe-common/models/application/store';
import { pcPlugins, mobilePlugins, ssrPlugins, tutorialPlugins, pureDocPlugins } from '@/utils/helpDoc/docDetailProcessor/config';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import { DocDetailTypeEnum } from '@/help-fe-common/constants/detail';
import { getSessionStorage, setSessionStorage } from '@/help-fe-common/utils/global/sessionStorage';
import styles from './index.module.scss';


const pluginsMap = {
  [DocDetailTypeEnum.NORMAL]: isMobile() ? mobilePlugins : pcPlugins,
  [DocDetailTypeEnum.PURE]: pureDocPlugins,
  [DocDetailTypeEnum.TUTORIAL]: tutorialPlugins,
  [DocDetailTypeEnum.SOLUTION]: tutorialPlugins,
};
async function fetchInitialData(
  urlPath: string,
  // CSR 下使用已经new 过的 Store，避免多份 Store 一直在内存中
  storeMap: {
    detailInnerStore: DetailModel;
  },
) {
  if (!urlPath) {
    return null;
  }

  const url = urlPath.indexOf('?') > -1 ? urlPath.substring(0, urlPath.indexOf('?')) : urlPath;

  const { detailInnerStore } = storeMap;

  await detailInnerStore.getDocumentDetail(url);

  return { storeData: detailInnerStore };
}

const DocDetail = ({ docDetailData }) => {
  const location = useLocation();
  const history = useHistory();
  const docHeight = getCssNumber('--help-doc-height');
  const isFirstRender = useRef(true);
  const isFirstCsr = useRef(true);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [initData, setInitData] = useState(docDetailData?.storeData || null);
  const [dynamicDocData, setDynamicDocData] = useState<any>({});
  const [asyncData, setAsyncData] = useState({ breadcrumb: [], recommendDocs: [], synopsis: [], tutorialData: {} as any });

  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);

  const {
    docInfo,
    showNoteTip,
    isNotFound,
    helpResponseCode,
    redirectUrl,
  } = useMemo(() => {
    if (!initData) return {};
    const { data } = initData || {};
    return {
      docInfo: { ...data, content: data?.content ? unescapeHtmlTag(data?.content) : data?.content },
      ...initData,
    };
  }, [initData]);

  // 是否以纯净模式文档展示
  const docDetailType: DocDetailTypeEnum = useMemo(() => {
    let docType = DocDetailTypeEnum.NORMAL;
    const urlParam = parseQuery(location?.search);
    // query参数解析
    if (urlParam?.mode === 'pure') {
      docType = DocDetailTypeEnum.PURE;
    }
    // 是否以教程文档展示
    const values = docInfo?.tags?.find((tagItem) => tagItem?.key === 'docTypes')?.value || [];
    // 教程文档预览则判定query参数
    if (urlParam?.mode === 'tutorial' || values?.includes('tutorial')) {
      docType = DocDetailTypeEnum.TUTORIAL;
    } else if (urlParam?.mode === 'solution' || values?.includes('solution')) {
      docType = DocDetailTypeEnum.SOLUTION;
    }
    return docType;
  }, [docInfo?.tags]);

  useEffect(() => {
    if (!docInfo) return;
    let breadcrumb = [];
    let recommendDocs = [];
    let synopsis: any = [];
    let tutorialData: any = {};
    if (!isServer()) {
      synopsis = extractSynopsisData(docInfo?.content);
      breadcrumb = initData?.breadcrumb || [];
      recommendDocs = docInfo?.recommendDocs || [];
      if (docDetailType === DocDetailTypeEnum.TUTORIAL || docDetailType === DocDetailTypeEnum.SOLUTION) {
        const tutorialProcessor = new TutorialProcessor(docInfo?.content);
        tutorialData = tutorialProcessor.extractTutorialData();
      }
    }
    setAsyncData({ breadcrumb, recommendDocs, synopsis, tutorialData });
  }, [docInfo]);

  /**
  * @param {Array} tags
  * [{key: "accept", value: "1", desc: "已承接"}, // 文档承接信息
  * {key: "translationSource", desc: "机翻"}] // 文档翻译来源
  */
  const docSourceInfo = useMemo(() => {
    return ['accept', 'translationSource'].map((key) => {
      return docInfo?.tags?.find((tagItem) => tagItem?.key === key)?.value || '';
    })?.filter((value) => value !== '')?.join(', ');
  }, [docInfo?.tags]);

  const { currentSynopsisId, onSynopsisClick } = useOutlineClick({ synopsis: asyncData?.synopsis });
  useChangeTitle({ title: docInfo?.seoTitle || docInfo?.title });

  const computeProgress = () => {
    const totalMilliseconds = 2000;
    const updateInterval = 20;

    // 新计时器重置进度值
    const intervalTimer = setInterval(() => {
      setLoadingProgress((oldProgress) => {
        const increment = (updateInterval / totalMilliseconds) * 100;
        const newProgress = Math.min(oldProgress + increment, 100);
        return newProgress >= 99 ? 99 : newProgress;
      });
    }, updateInterval);

    return intervalTimer;
  };

  const setInitialScrollPosition = (navHeight) => {
    document.body.scrollTop = navHeight;
    document.documentElement.scrollTop = navHeight;
  };

  /**
   * 数据请求，ssr读取属性，csr请求接口。
   * @param isNewDoc isNewDoc表示切换新文档，走CSR重新请求
   * @param  intervalTimer 请求计时器，结束时清除
   */
  const fetchData = (isNewDoc, intervalTimer) => {
    setIsLoading(true);

    // SSR getInitialProps 没有值时的兜底逻辑。isNewDoc表示切换新文档，走CSR重新请求
    const dataPromise = docDetailData && !isNewDoc
      ? Promise.resolve(docDetailData)
      : fetchInitialData(`${location.pathname}`, {
        detailInnerStore: new DetailModel(),
      });
    dataPromise
      .then((res) => {
        if (res === null) {
          sendSlsLog({ page: 'documentDetail', section: '', action: 'error', userParams1: 'ssr initProps is null' });
        }
        // 初始化数据
        const { storeData } = res;
        setInitData(storeData);
        storeDataMerge(detailStore, storeData);

        // 设置全局变量nodeId
        window.globalData = window.globalData || {};
        window.globalData.nodeId = storeData?.data?.nodeId;

        clearInterval(intervalTimer);
        setLoadingProgress(100);
        setTimeout(() => {
          setIsLoading(false);
          setLoadingProgress(0);
        }, 300);

        if (isFirstCsr.current && !window.__ICE_SSR_ENABLED__) {
          isFirstCsr.current = false;
          const csrFistPaintTime = Date.now() - performance.timing.navigationStart;
          sendSlsLog({
            page: 'documentDetail',
            section: '',
            action: 'csrFirstPaint',
            userParams1: csrFistPaintTime,
            userParams2: Date.now(),
            userParams3: performance.timing.navigationStart,
          });
        }
        if (isFirstRender.current) {
          // 标记后续渲染不再是首次
          isFirstRender.current = false;
          return;
        }

        // 接口数据返回时，设置滚动条初始位置
        let navHeight = getCssNumber('--default-nav-height');
        const currentScrollTop = getSessionStorage(storeData?.data?.nodeId);

        if (currentScrollTop) {
          navHeight = currentScrollTop;
        }
        setInitialScrollPosition(navHeight);
      })
      .catch((err) => {
        sendSlsLog({ page: 'documentDetail', section: '', action: 'error', userParams1: JSON.stringify(err) });
        clearInterval(intervalTimer);
      });
  };

  useEffect(() => {
    const { nodeId, alias } = identifier || {};
    const isNewDoc = (Number(nodeId) !== docInfo?.nodeId && !isSameAlias(String(alias), docInfo?.alias));

    const intervalTimer = computeProgress();

    // 数据请求，ssr读取属性，csr请求接口
    fetchData(isNewDoc, intervalTimer);

    // SSR场景下，需要获取动态数据
    if (docDetailData) {
      const dynamicDataPromise = detailStore.getDetailDynamicData({ nodeId, alias });
      dynamicDataPromise.then((res) => {
        res && setDynamicDocData(res);
      });
    }

    // 组件卸载时清除定时器
    return () => clearInterval(intervalTimer);
  }, [identifier]);

  useEffect(() => {
    if (!location?.pathname || !window?.globalData?.nodeId) return;
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    const nodeId = window.globalData?.nodeId;
    setSessionStorage(nodeId, scrollTop);
  }, [location?.pathname]);

  useEffect(() => {
    if (redirectUrl) {
      triggerUrlBehavior(redirectUrl, history, { from: 'detail' }, '', 'replace');
    }
  }, [redirectUrl]);

  const ssrContentProcessor = () => {
    const params = { history, data: docInfo, plugins: ssrPlugins };

    const parser = new DOMParser();
    const dom = parser?.parseFromString(docInfo?.content || '', 'text/html');

    ssrPlugins.forEach((item) => {
      const plugin = item as any;
      const [process] = plugin;
      if (process) {
        process(params, dom);
      }
    });
  };

  if (isServer() && docInfo?.nodeId) {
    ssrContentProcessor();
  }

  /**
   * 调用plugin插件
   */
  useEffect(() => {
    let unbindEventArr;
    // if (isServer()) return;
    if (docInfo?.nodeId) {
      try {
        // 设置异步任务，保证在数据完全处理完成后才执行插件逻辑
        setTimeout(() => {
          unbindEventArr = contentProcessor({ history, data: docInfo, plugins: pluginsMap[docDetailType] });
        }, 10);
      } catch (e) {
        sendSlsLog({
          page: 'documentDetail',
          section: 'documentDetail',
          action: 'detailJsRenderError',
          userParams1: JSON.stringify({ message: e.message, name: e.name, stack: e.stack }),
        });
      }
    }
    return () => {
      unbindEventArr?.length && unbindEventArr?.forEach((item) => item && item());
    };
  }, [docInfo?.nodeId]);

  if (isNotFound) {
    return (
      <div className={styles.contentWrapper}>
        <NotFound responseCode={helpResponseCode} />
      </div>);
  }

  if (docDetailType === DocDetailTypeEnum.PURE) {
    return (
      <div>
        <PureDoc docInfo={docInfo} />
      </div>
    );
  } else if (docDetailType === DocDetailTypeEnum.SOLUTION || docDetailType === DocDetailTypeEnum.TUTORIAL) {
    return (
      <div className={styles.contentWrapper}>
        {
          asyncData?.tutorialData?.manual &&
          <Tutorial docData={docInfo} tutorialType={docDetailType} tutorialData={asyncData?.tutorialData} />
        }
      </div>
    );
  }

  return (
    <div className={styles.contentWrapper}>
      <section
        className="aliyun-docs-content"
        style={{ height: docInfo ? 'auto' : `${docHeight}px` }}
      >
        <Header
          breadcrumb={asyncData?.breadcrumb}
          docInfo={docInfo}
          dynamicDocInfo={dynamicDocData}
        />
        {
          showNoteTip &&
          <NoteTip tipId="help.doc.noteTip" tipType="help.doc.tipType" />
        }
        <Detail data={docInfo} />
        <RecommendDoc recommendDocs={asyncData?.recommendDocs} />
        {/* 反馈按钮区域 */}
        {
          docInfo?.nodeId &&
          <FeedbackButton docInfo={docInfo} />
        }
      </section>
      <div className="aliyun-docs-side" id="aliyun-docs-side">
        <div
          id="aliyun-docs-side-content"
          className={classNames('aliyun-docs-side-content', styles.rightBox)}
        >
          <Outline
            data={asyncData?.synopsis}
            currentId={currentSynopsisId}
            docSourceInfo={docSourceInfo}
            onClick={onSynopsisClick}
          />
        </div>
        <div id="aliyun-docs-side-blank" className="aliyun-docs-side-blank" />
      </div>
      {isLoading && <div className={styles.loadingProgress} style={{ width: `${loadingProgress}%` }} />}
    </div >
  );
};

DocDetail.getInitialProps = async (ctx) => {
  if (!isServer()) {
    return { docDetailData: null };
  }

  const url = ctx?.req?.url || '';

  const res = await fetchInitialData(url, {
    detailInnerStore: new DetailModel(),
  });

  return { docDetailData: res };
};

DocDetail.pageConfig = {
  spm: '11186623',
};

export default DocDetail;
