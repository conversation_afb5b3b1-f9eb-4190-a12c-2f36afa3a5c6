import React, { useState, useMemo, useEffect } from 'react';
import { Loading } from '@alifd/next';
import classNames from 'classnames';
import { isEmpty, throttle } from 'lodash';
import FullColWrapper from '@/help-fe-common/components/common/FullColWrapper';
import Banner from '@/solution/components/Index/IndexBanner';
import AllSolution from '@/solution/components/Index/AllSolution';
import CustomerCase from '@/solution/components/Index/CustomerCase';
import { SolutionIndexModel } from '../../models/SolutionIndexModel';
import Consult from '@/solution/components/Common/Consult';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import styles from './index.module.scss';

async function fetchInitialData(solutionModel) {
  await solutionModel.getIndex();

  return { storeData: solutionModel };
}
const SolutionIndex = ({ solutionData }) => {
  const [indexModuleData, setIndexModuleData] = useState(solutionData?.storeData?.data || []);

  const { bannerData, hotTopicData, customerData, solutionTreeData } = useMemo(() => {
    if (!indexModuleData) return { bannerData: {}, hotTopicData: [], customerData: {}, solutionTreeData: {} };
    const moduleData = {
      bannerData:
        indexModuleData?.find((item) => item.type === 'banner')?.data || {},
      hotTopicData:
        indexModuleData?.find((item) => item.type === 'hot_topic_recommend')?.data || [],
      customerData:
        indexModuleData?.find((item) => item.type === 'customer_case')?.data || {},
      solutionTreeData:
        indexModuleData?.find((item) => item.type === 'menu_tree')?.data || {},
    };
    return moduleData;
  }, [indexModuleData]);// 初始数据

  useEffect(() => {
    const dataPromise = !isEmpty(solutionData)
      ? Promise.resolve(solutionData)
      : fetchInitialData(new SolutionIndexModel());
    dataPromise
      .then((res: any) => {
        if (res === null) {
          console.error('csr 渲染问题');
        }
        // 初始化数据
        const { storeData } = res;
        setIndexModuleData(storeData?.data);
      })
      .catch((err) => {
        console.error('[Solution detail] error: ', err);
      });
  }, []);

  useEffect(() => {
    const exposedArr: number[] = [];
    const sectionArr = [
      'banner',
      'allSolution',
      'customerCase',
      'consult',
    ];
    // 监听曝光
    window.addEventListener('scroll', throttle(() => {
      document.querySelectorAll(`.${styles.indexContainer} > *`).forEach((target, index) => {
        if (exposedArr.includes(index)) {
          return;
        }

        const isInViewPort = target.getBoundingClientRect().top < window.innerHeight;
        if (isInViewPort) {
          exposedArr.push(index);
          sendSlsLog({ page: 'solutionIndex', section: sectionArr[index], action: 'pv' });
        }
      });
    }, 200));
  }, []);

  return (
    <div className={classNames('aliyun-solution-detail', styles.indexContainer)} >
      {isEmpty(indexModuleData) ? (
        <Loading className={styles.loading} />
      ) : (
        <>
          <FullColWrapper >
            <div className={classNames('col-extend-left', 'col-extend-right', styles.top)}>
              <Banner data={bannerData} hotTopicData={hotTopicData} />
            </div>
          </FullColWrapper>
          <FullColWrapper >
            <AllSolution data={solutionTreeData} />
          </FullColWrapper>
          <FullColWrapper >
            <CustomerCase data={customerData} />
          </FullColWrapper>
          <FullColWrapper >
            <Consult type="index" />
          </FullColWrapper>
        </>
      )}
    </div>
  );
};

SolutionIndex.getInitialProps = async () => {
  if (!isServer()) {
    return { solutionData: null };
  }

  const res = await fetchInitialData(new SolutionIndexModel());

  return { solutionData: res };
};

SolutionIndex.pageConfig = {
  spm: '29338757',
};

export default SolutionIndex;

