import { addImgContainer } from '@/help-fe-common/utils/global/previewImg';
import React from 'react';
import ReactDOM from 'react-dom';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import MobileImgPreview from '@/help-fe-common/components/mobile/ImgPreview';
import PcImgPreview from '@/help-fe-common/components/pc/ImgPreview';

const childClass = 'img.image.break';
const rootClass = '.markdown-body';
const tutorialClass = '.help-tutorial-container .markdown-body';

// 获取所有符合条件的图片 DOM 元素
const getImgDOMList = () => document.querySelectorAll(`${rootClass} ${childClass}`);

// 创建图片预览容器并插入到文档中
const createPreviewContainer = (markdownDOM: HTMLElement) => {
  const previewContainer = document.createElement('div');
  previewContainer.id = 'img-preview-container';
  markdownDOM?.insertBefore(previewContainer, markdownDOM.firstChild);
};

// 处理图片元素，添加懒加载和背景容器
const processImages = (imgDOMList: NodeListOf<Element>, tutorialDOM: HTMLElement | null) => {
  imgDOMList.forEach((imgItem: HTMLImageElement) => {
    console.log(imgItem.getAttribute('data-tag'), 'imgItem');
    if (!imgItem) return;

    // 设置图片懒加载属性
    imgItem.setAttribute('loading', 'lazy');

    // 如果图片位于教程文档中且不是行内图片，则添加背景容器
    const isTutorialImg = tutorialDOM?.contains(imgItem) && !imgItem.classList.contains('inline');
    if (isTutorialImg) {
      addImgContainer(imgItem);
    }
  });
};

// 绑定图片点击事件，用于展示图片预览
const bindImageClickEvents = (imgDOMList: NodeListOf<Element>) => {
  const onClosePreview = () => {
    document.body.removeAttribute('style');
    ReactDOM.render(null, document.getElementById('img-preview-container'));
  };

  imgDOMList.forEach((imgItem: HTMLImageElement) => {
    if (!imgItem || imgItem.classList.contains('explorer-button-img')) return;

    imgItem.addEventListener('click', () => {
      const PreviewComponent = isMobile() ? MobileImgPreview : PcImgPreview;
      ReactDOM.render(
        React.createElement(PreviewComponent, { data: imgItem, onClose: onClosePreview }),
        document.getElementById('img-preview-container'),
      );
    });
  });
};

// 主处理函数
const process = (params, contentDom) => {
  const rootDom = contentDom || document;
  const imgDOMList = rootDom.querySelectorAll(`${childClass}`);
  const tutorialDOM = rootDom.querySelector(tutorialClass) as HTMLElement;
  console.log(contentDom, imgDOMList, 'imgDOMList');

  if (!imgDOMList.length) return;

  // 处理图片元素
  processImages(imgDOMList, tutorialDOM);
};

// 主绑定事件函数
const bindEvent = () => {
  const imgDOMList = getImgDOMList();
  const markdownDOM = document.querySelector(rootClass) as HTMLElement;
  // 创建图片预览容器
  createPreviewContainer(markdownDOM);
  bindImageClickEvents(imgDOMList);
};

export default [process, bindEvent];
