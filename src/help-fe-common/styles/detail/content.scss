@import '../../../help-fe-common/styles/variables.scss';

// 特殊符号√字体设置
@font-face {
  font-family: 'help-font';
  src: local('Helvetica Neue');
  unicode-range: U+221a;
}

html {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.unionContainer {
  .markdown-body {
    color: $text-color;
    font-size: $text-size;
    font-family: help-font, helvetica neue, pingfang SC, arial, hiragino sans gb, microsoft yahei ui, microsoft yahei, simsun, sans-serif;
    line-height: $text-line-height;
    word-wrap: break-word;
    margin: 24px 0;
    overflow: hidden;

    /////////////////////// 区块元素margin设置 ////////////////////
    dl,
    ol,
    table,
    ul {
      margin-top: 16px;
      margin-bottom: 24px;
    }

    /////////////////////// 段落 ////////////////////
    p,
    div.p {
      font-size: $text-size;
      line-height: $text-line-height;
      font-weight: 400;
      letter-spacing: 0.4px;
      word-break: break-word;
      margin: 0;
      margin-bottom: 16px;
    }

    // 段落缩进及首行缩进
    p[indent] {
      text-indent: 2em;
    }

    p[left='2'] {
      padding-left: 2em;
    }

    p[left='4'] {
      padding-left: 4em;
    }

    p[left='6'] {
      padding-left: 6em;
    }

    p[left='8'] {
      padding-left: 8em;
    }

    p.shortdesc:empty {
      display: none;
    }

    // 中英文间距
    .help-letter-space {
      display: inline-block;
      width: 0.3em;
    }

    ///////////////////// 标题 ////////////////////////
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      padding: 0;
      margin-top: 24px;
      margin-bottom: 16px;
      color: $text-color;
      font-weight: bold;
      letter-spacing: 0.4px;
    }

    h1 {
      font-size: 34px;
    }

    h2 {
      font-size: 26px;
      line-height: 32px;
      // 正文h2加分割线
      padding-top: 24px;
      border-top: 1px solid #e9e9e9;
    }

    h3 {
      font-size: 18px;
    }

    h4 {
      font-size: 16px;
    }

    h5 {
      font-size: 15px;
    }

    h6 {
      font-size: 14px;
    }

    /////////////////// 链接 ///////////////////
    a {
      text-decoration: none;
      outline: none;
      background: 0 0;
      color: $text-link-color;

      &:visited {
        color: $text-link-color;
      }

      &:hover {
        color: $text-link-color;
      }

      &:active {
        color: $text-link-color;
      }

      &:link {
        color: $text-link-color;
      }

      &:focus {
        color: $text-link-color;
      }
    }


    /////////////////// 特殊标签 ///////////////////
    // 视频

    video {
      width: 100%;
      height: auto;
      max-height: 100%;
      max-width: 600px;
      aspect-ratio: 16 / 9;
    }

    table {
      video {
        width: 100%;
        height: auto;
        aspect-ratio: unset;
      }
    }

    // 分割线
    hr {
      overflow: hidden;
      height: 4px;
      padding: 0;
      margin: 16px 0;
      background-color: #e7e7e7;
      border: 0 none;
    }

    // 加粗
    strong {
      font-weight: 700;
    }

    // 引用
    blockquote {
      padding: 0 15px;
      color: #777;
      border-left: 4px solid #ddd;

      &> :first-child {
        margin-top: 0;
      }

      &> :last-child {
        margin-bottom: 0;
      }
    }

    // 处理var标签为斜体不加粗
    var {
      font-style: italic;
      font-weight: 400;
    }

    // 处理i，em标签为斜体
    i,
    em {
      font-style: italic;
    }

    // 存在class属性视为图标，正常展示
    i[class] {
      font-style: normal;
    }

    // 用户键盘输入
    kbd {
      background-color: #e7e7e7;
      background-image: -webkit-linear-gradient(#fefefe, #e7e7e7);
      background-image: linear-gradient(#fefefe, #e7e7e7);
      background-repeat: repeat-x;
      border-radius: 2px;
      border: 1px solid #cfcfcf;
      color: #000;
      padding: 3px 5px;
      line-height: 10px;
      font: 12px Consolas, 'Liberation Mono', Menlo, Courier, monospace;
      display: inline-block;
    }

    .anchor {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      display: block;
      margin-left: -30px;
      padding-right: 6px;
      padding-left: 30px;
    }

    .anchor:focus {
      outline: 0;
    }

    // dl dd dt
    dl {
      padding: 0;

      dt {
        margin-top: 16px;

        padding: 0;
        font-weight: 700;
        font-size: 1em;
        font-style: italic;
      }

      dd {
        margin-left: 0;
        margin-bottom: 16px;
        padding: 0 16px;
      }
    }

    // 加粗
    .cmdname,
    .parmname,
    .b,
    .uicontrol,
    .menucascade,
    .chhead,
    .choption,
    .caution,
    .kwd,
    .wintitle,
    .danger {
      font-weight: 700;
    }

    /////////////////// api文档 ///////////////////
    h1 .octicon-link,
    h2 .octicon-link,
    h3 .octicon-link,
    h4 .octicon-link,
    h5 .octicon-link,
    h6 .octicon-link {
      display: none;
      color: #000;
      vertical-align: middle;
    }

    h1:hover .anchor,
    h2:hover .anchor,
    h3:hover .anchor,
    h4:hover .anchor,
    h5:hover .anchor,
    h6:hover .anchor {
      height: 1em;
      padding-left: 8px;
      margin-left: -30px;
      line-height: 1;
      text-decoration: none;
    }

    h1:hover .anchor .octicon-link,
    h2:hover .anchor .octicon-link,
    h3:hover .anchor .octicon-link,
    h4:hover .anchor .octicon-link,
    h5:hover .anchor .octicon-link,
    h6:hover .anchor .octicon-link {
      display: inline-block;
    }

    /////////////////// 自定义foreign标签 ///////////////////
    // 自定义html文档样式
    .aliware-tile {
      min-height: 130px;
      height: auto !important;
      font-size: $text-second-size;
      position: unset;

      .aliware-default-lite {
        position: unset;
      }

      ul.aliware-tile-list-bullet {
        list-style: disc;
        list-style-position: inside;
        padding-left: 5px !important;
        font-size: 12px;
        margin-bottom: 0;

        li {
          list-style-position: inside;
          font-size: 12px;
          width: 90%;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          color: #ff6a00;
          margin: 0;
        }
      }

      p,
      span,
      a {
        font-size: $text-second-size;
      }
    }

    /////////////////// api ///////////////////
    // api文档中调试按钮
    .api_explorer p {
      cursor: text;

      a {
        pointer-events: none;

        &:visited {
          color: #333;
        }

        &:hover {
          color: #333;
        }

        &:active {
          color: #333;
        }

        &:link {
          color: #333;
        }

        &:focus {
          color: #333;
        }
      }
    }

    .apiexplorer-click {
      position: relative;

      >.p {
        background: rgba(242, 242, 242, 0.65);
        padding: 20px;

        a {
          color: #333;
        }
      }

      >.explorer-button {
        width: 75px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        background: #ff6a00;
        color: #fff;
        position: absolute;
        top: 24px !important;
        right: 0;
        cursor: pointer;

        .explorer-button-img {
          display: inline-block;
          width: 13px;
          margin-right: 3px;
          margin-top: -2px;
          vertical-align: middle;
        }

        // amp平台新api文档中button图片处理
        .image.break {
          height: auto;
          box-shadow: none;
          margin: 0;
        }

        &:hover {
          background: #ff791a;
        }
      }
    }

    /////////////////// cite ///////////////////
    .icms-help-docs-content[lang='en-US'] .cite {
      font-style: italic;
    }

    .icms-help-docs-content[lang='ja-JP'] .cite,
    .icms-help-docs-content[lang='zh-CN'] .cite,
    .icms-help-docs-content[lang='zh-TW'] .cite {
      font-style: normal;

      .citeleft,
      .citeright {
        display: inline;
      }
    }

    .cite .citeleft,
    .cite .citeright {
      display: none;
    }

  }
}