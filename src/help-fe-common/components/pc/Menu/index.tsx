import React, { useEffect, useState, useMemo } from 'react';
import { useHistory, useLocation, useRequest } from 'ice';
import classnames from 'classnames';
import ProductList from '@/help-fe-common/components/common/ProductList';
import ProductSelect from '@/help-fe-common/components/common/ProductIndex/ProductSelect';
import MenuSearch from './MenuSearch';
import useDrag from '@/help-fe-common/hooks/useDrag';
import services from '@/help-fe-common/services';
import { generateUrl } from '@/help-fe-common/utils/global/url/generateUrl';
import { aLinkTrigger } from '@/help-fe-common/utils/global/url/aLinkTrigger';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { getHighlightMenuStack, changeOpenStatus, generateSpmd, replaceScmHash } from '@/help-fe-common/utils/helpDoc/menu';
import { getProductInfo } from '@/help-fe-common/utils/helpDoc/searchStorage';
import { getCssNumber } from '@/help-fe-common/utils/global/style/cssValue';
import { headHeight } from '@/help-fe-common/constants';
import { isCN } from '@/help-fe-common/utils/global/website';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import isMobile from '@/help-fe-common/utils/global/isMobile';
import styles from './index.module.scss';

interface IProps {
  menuData: {
    newProductInfo: any;
    searchProductInfo: any;
    productList: any;
    newTreeList: any;
  };
  onClose?: (showMenu: boolean) => void;
}

export default ({ menuData, onClose }: IProps) => {
  const history = useHistory();
  const location = useLocation();
  const { newProductInfo,
    searchProductInfo,
    productList,
    newTreeList } = menuData;

  const [productInfo, setProductInfo] = useState<any>(null);
  const [showProductList, setShowProductList] = useState(false);
  const [highlightMenuStack, setHighlightMenuStack] = useState<any[]>([]);
  const [treeList, setTreeList] = useState([]);
  const identifier = useMemo(() => extractUrlParam(location?.pathname), [location?.pathname]);
  const { dragOffset, onDragging } = useDrag();
  const { data: productCode, request: getProductCode } = useRequest(services.getPipCode);

  useEffect(() => {
    if (newProductInfo) {
      // 设置目录拖动方法
      onDragging('.help-menu-drag-box');
    }
  }, [newProductInfo]);


  useEffect(() => {
    if (!searchProductInfo || !newProductInfo) return;
    // 中国站，由于存在子产品搜索场景，从productInfo中读取id和title缓存至本地
    if (isCN) {
      const { id: productId, title: name, level } = searchProductInfo;
      window.localStorage.setItem('productInfo', JSON.stringify({ id: productId, name, level }));
      window.helpProductInfo = { productId, name, level };
    } else {
      const { id: productId } = newProductInfo;
      window.localStorage.setItem('productId', productId);
    }
  }, [searchProductInfo, newProductInfo]);

  useEffect(() => {
    if (!newProductInfo || !newTreeList?.length) return;
    const state: any = history?.location?.state;
    if (newProductInfo?.id !== productInfo?.id || state?.from !== 'menu') {
      setProductInfo(newProductInfo);
      const menuStack = getHighlightMenuStack(newTreeList);
      setHighlightMenuStack(menuStack);
      setTreeList(
        changeOpenStatus(newTreeList, menuStack, null, null, searchProductInfo?.showType, newProductInfo?.id),
      );
      // 产品切换时请求pipCode并保存至globalData
      getProductCode({ productId: newProductInfo?.id });
    }
  }, [newProductInfo, newTreeList, history?.location?.state]);

  useEffect(() => {
    if (productCode) {
      window.globalData = window.globalData || {};
      window.globalData.productCode = productCode;
    }
  }, [productCode]);

  useEffect(() => {
    if (!highlightMenuStack) return;
    try {
      const state: any = history?.location?.state;
      if (state?.from === 'menu') return;
      const highlightId = highlightMenuStack?.[highlightMenuStack?.length - 1]?.id;
      const highlightNode = document.querySelector(`.aliyun-docs-toc-content li[id="${highlightId}"]`) as HTMLElement;
      const menuContainer = document.querySelector('.help-menu-scroll-container');
      const offsetTop = highlightNode?.offsetTop || 0;
      menuContainer && menuContainer?.scrollTo({ top: offsetTop, behavior: 'smooth' });
    } catch (error) {
      console.error(error);
    }
  }, [highlightMenuStack, history?.location?.state]);

  // 监听浏览器popState行为
  useEffect(() => {
    // 监听浏览器回退/前进（popState）、刷新（load）行为
    window.addEventListener('popstate', onPopState);
    window.addEventListener('load', onPopState);
    return () => {
      window.removeEventListener('popstate', onPopState);
      window.removeEventListener('load', onPopState);
    };
  }, []);

  // 点击浏览器回退、下一页触发
  const onPopState = () => {
    if (!history?.location?.hash) {
      const state = history?.location?.state || {};
      history.location.state = { ...state, id: identifier?.nodeId, from: 'browser' };
    }
  };

  // 获取productList top定位
  const getProductListTop = () => {
    const scrollTop = document.body.scrollTop || document.documentElement.scrollTop;
    const navHeight = getCssNumber('--default-nav-height');
    if (scrollTop <= navHeight) {
      return headHeight + navHeight - scrollTop;
    } else {
      return headHeight;
    }
  };

  /**
   * 埋点统计展开一级目录的pv
   * @param nodeInfo 点击节点信息
   * @param stack menuStack
   */
  const menuClickSlsLogger = (nodeInfo, stack) => {
    const title = stack?.[0]?.title || '';
    const productId = getProductInfo()?.id || '';
    // useParams1: 一级目录title，userParams2: 产品id
    sendSlsLog({ page: '', section: 'menuTree', action: 'menuExpand', userParams1: title, userParams2: productId, docId: nodeInfo?.id });
  };

  // menu点击相关
  const onMenuItemClick = (e, nodeInfo, stack) => {
    e?.preventDefault();
    e.stopPropagation();
    // 点击当前文档节点时，不触发跳转行为
    if (nodeInfo?.id === highlightMenuStack?.[highlightMenuStack?.length - 1]?.id) {
      return;
    }
    const time = Date.now();
    setHighlightMenuStack(stack);
    aLinkTrigger(e, history, { time, id: nodeInfo.id, from: 'menu' }, 'historyPush');
    menuClickSlsLogger(nodeInfo, stack);
    // 移动端点击行为后需要隐藏目录组件
    isMobile() && onClose && onClose(false);
  };

  const handleClick = (e, menuStack) => {
    e?.preventDefault();
    e.stopPropagation();
    const data = changeOpenStatus(treeList, menuStack, null, 'click', null, productInfo?.id);
    setTreeList(data);
  };

  const onProductMouseLeave = (e, className) => {
    if (e?.relatedTarget?.closest?.(className)) {
      // 忽略事件，因为它可能是由输入法引起的
      return;
    }
    onMouseHover(false);
  };


  const onMouseHover = (status) => {
    setShowProductList(status);
  };

  const renderMenuTree = (list, level, stack) => {
    return list?.map((item, index) => {
      const { children, ...nodeInfo } = item;
      if (!children || children?.length === 0) {
        return (
          <li
            key={nodeInfo.id}
            id={nodeInfo?.id}
            className={classnames(
              styles[`level${level}`],
              nodeInfo?.id === highlightMenuStack?.[level - 1]?.id ? styles.highlight : '',
            )}
          >
            <a
              id={nodeInfo?.id}
              data-spm={generateSpmd(nodeInfo?.pathArr)}
              data-tracker-scm={replaceScmHash(nodeInfo?.scm) || ''}
              href={generateUrl(nodeInfo)}
              onClick={(e) => onMenuItemClick(e, nodeInfo, [...stack, nodeInfo])}
            >
              <span className={styles.menuItemText} id={nodeInfo?.id}>{nodeInfo?.title}</span>
            </a>
          </li>
        );
      }

      const renderIcon = (open) => {
        return open ? 'help-icon-open-arrow' : 'help-icon-close-arrow';
      };

      return (
        <li
          key={nodeInfo?.id}
          id={nodeInfo?.id}
          className={classnames(
            styles[`level${level}`],
            nodeInfo?.open ? styles.open : '',
            nodeInfo?.id === highlightMenuStack?.[highlightMenuStack?.length - 1]?.id ? styles.highlight : '',
          )}
        >
          <a
            id={nodeInfo?.id}
            data-spm={generateSpmd(nodeInfo?.pathArr)}
            data-tracker-scm={replaceScmHash(nodeInfo?.scm) || ''}
            href={generateUrl(nodeInfo)}
            onClick={(e) => {
              onMenuItemClick(e, nodeInfo, [...stack, nodeInfo]);
              handleClick(e, [...stack, nodeInfo]);
            }}
          >
            <i
              className={classnames(
                'help-iconfont',
                renderIcon(nodeInfo?.open),
              )}
              id={nodeInfo?.id}
              onClick={(e) => {
                handleClick(e, [...stack, nodeInfo]);
              }}
            />
            <span
              className={styles.menuItemText}
              id={nodeInfo?.id}
            >
              {nodeInfo?.title}
            </span>
          </a>
          {
            nodeInfo?.open ?
              <ul>
                {renderMenuTree(children, level + 1, [...stack, nodeInfo])}
              </ul> :
              null
          }
        </li>
      );
    });
  };

  /**
   * ssr渲染时，产品名设为空；csr渲染时，读取模板中productTitle作为初始化渲染，防止白屏
   * @returns productTitleDOM
   */
  const renderMenuTitle = () => {
    if (isServer()) {
      return <div className="help-menu-title" />;
    }
    return (
      <div className="help-menu-title">
        <a
          className={styles.menuTitleText}
          href={productInfo?.url ? productInfo?.url : `/product/${productInfo?.id}.html`}
          onClick={(e) => aLinkTrigger(e, history)}
        >
          {productInfo?.title || window.menuTreeInfo?.productTitle}
        </a>
      </div>
    );
  };

  /**
   * ssr渲染时，如果存在子产品，设置占位区块
   * @returns subProductNode
   */
  const renderSubproductNode = () => {
    if (isServer()) {
      return <div className="help-menu-subproduct" />;
    }
    if (productList?.length) {
      return (productList?.length > 1 && <ProductSelect productList={productList} dataSpm={`help-menu-sub-${newProductInfo?.id}`} />);
    } else {
      // 仅页面初始化时展示占位，否则会引起产品切换时默认占位
      return (
        productInfo === null &&
        <div className="help-menu-subproduct" dangerouslySetInnerHTML={{ __html: window.menuTreeInfo?.subProductHtml || '' }} />
      );
    }
  };

  /**
   * ssr渲染时，目录树dom节点为空；csr渲染时，读取模板中menuDOM作为初始化渲染，防止白屏
   * @returns menuDOM
   */
  const renderMenuContent = () => {
    if (isServer()) {
      return <ul id="common-menu-container" />;
    }
    if (treeList?.length) {
      return (
        <ul id="common-menu-container">
          {renderMenuTree(treeList, 1, [])}
        </ul>
      );
    } else {
      return <ul id="common-menu-container" dangerouslySetInnerHTML={{ __html: window.menuTreeInfo?.menuTreeHtml || '' }} />;
    }
  };

  return (
    <div className={styles.helpMenuBox} style={dragOffset ? { width: `${dragOffset}px` } : {}}>
      <div className={classnames(styles.helpMenuInnerBox, 'aliyun-docs-toc-content')} style={dragOffset ? { width: `${dragOffset}px` } : {}}>
        <div className={classnames(styles.helpMenuDrag, 'help-menu-drag-box')} />
        <div className={styles.helpMenu} >
          <div className={styles.helpMenuTop}>
            {
              renderMenuTitle()
            }
            <span
              className={styles.iconBox}
              onMouseEnter={() => onMouseHover(true)}
              onMouseLeave={() => onMouseHover(false)}
            >
              <i
                className={classnames('help-iconfont', 'help-icon-mulushu-shouqiicon', showProductList ? styles.iconExpand : styles.iconFold)}
              />
            </span>
          </div>
          {renderSubproductNode()}
          <MenuSearch newTreeList={newTreeList} dataSpm={`help-menu-search-${productInfo?.id}`} onClose={onClose} />
          <div className={classnames('help-menu-scroll-container', styles.menuContent)} data-spm={`help-menu-${productInfo?.id}`}>
            {renderMenuContent()}
          </div>
        </div>
      </div>
      <div
        className={classnames(styles.productList)}
        style={showProductList ? {
          display: 'block',
          left: dragOffset ? `${dragOffset - 4}px` : '296px',
          top: `${getProductListTop()}px`,
          height: `calc(100vh - ${getProductListTop()}px)`,
          width: `calc(100vw - ${dragOffset}px)`,
        } : {
          display: 'none',
        }}
      >
        <ProductList
          onMouseEnter={() => onMouseHover(true)}
          onMouseLeave={onProductMouseLeave}
        />
      </div>
    </div >
  );
};
