// 获取文档中的所有 audio 元素
const getAudioElements = (): HTMLAudioElement[] => {
  const audioSelector = '.markdown-body audio'; // 提取选择器为变量
  const audioDomList: NodeListOf<HTMLAudioElement> = document.querySelectorAll(audioSelector);
  return Array.from(audioDomList || []); // 简化为空数组的处理
};

// 暂停所有非当前索引的音频并重置播放进度
const pauseOtherAudios = (currentIndex: number) => {
  const audioElements = getAudioElements();
  audioElements.forEach((audio, index) => {
    if (index !== currentIndex) {
      audio.pause(); // 暂停音频
      audio.currentTime = 0; // 重置播放进度
    }
  });
};

// 为所有 audio 元素设置初始属性
const process = () => {
  const audioElements = getAudioElements();
  if (audioElements.length === 0) return; // 提前返回，避免空列表处理

  audioElements.forEach((audio) => {
    audio.setAttribute('preload', 'none'); // 禁用预加载
    audio.setAttribute('controlslist', 'nodownload noplaybackrate'); // 禁用下载和播放速率调整
  });
};


// 为每个 audio 元素绑定 play 事件监听器
const bindEvent = () => {
  const audioElements = getAudioElements();
  if (audioElements.length === 0) return;

  audioElements.forEach((audio, index) => {
    audio.addEventListener('play', () => {
      pauseOtherAudios(index); // 暂停其他音频
    });
  });
};

export default [process, bindEvent];
