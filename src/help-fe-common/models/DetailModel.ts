import { getInitialData } from 'ice';
import service from '@/help-fe-common/services/apiService';
import { WEBSITE, LANG, CHANNEL } from '@/help-fe-common/utils/global/website';
import { extractUrlParam } from '@/help-fe-common/utils/global/url/urlExtract';
import { extractDocumentDetail } from '@/help-fe-common/utils/helpDoc/docDetail';
import { PAGE_TYPE } from '@/help-fe-common/constants/index';
import { escapeHtmlTag } from '@/help-fe-common/utils/helpDoc/escapeHtmlTag';
import addInitContentSpace from '@/help-fe-common/utils/helpDoc/addSpace';
import { isServer } from '@/help-fe-common/utils/node/getContext';
import { sendSlsLog } from '../utils/global/track/slsLogger';
import { ssrPlugins } from '@/utils/helpDoc/docDetailProcessor/config';

export class DetailModel {
  // 文档别名、id
  alias = '';
  nodeId = '';

  data: any = {};

  breadcrumb: any = [];

  // 是否是机器翻译
  isMachineTranslation = false;

  // 是否显示NoteTip
  showNoteTip = false;

  // 是否显示notfound页
  isNotFound = false;

  // 响应状态码
  helpResponseCode = 200;
  redirectUrl = '';
  helpDocVersion = 1;

  // 获取文档详情信息
  // page：页码，per_page：每页条数，search：搜索内容，archived 表示是否归档（也就是删除的意思）, 排序（按第一个字段排序，再按第二个字段排序）：order_by: 筛选类型, sort: 升降顺序。visibility_level：10表示公开仓库，0为私有仓库
  getDocumentDetail = async (path) => {
    const params = {
      nodeId: '',
      alias: '',
      pageNum: 1,
      pageSize: 20,
      website: WEBSITE,
      language: LANG,
      channel: CHANNEL,
    };
    const identifier = extractUrlParam(path);
    if (!identifier) {
      this.isNotFound = true;
      return;
    }
    const { nodeId, alias } = identifier;
    params.nodeId = nodeId;
    params.alias = alias as string;
    this.nodeId = nodeId;
    this.alias = alias as string;

    const docInitialData = getInitialData();

    // 如果有缓存，且为当前文档的数据
    if (docInitialData &&
      JSON.stringify(docInitialData) !== '{}' &&
      docInitialData?.pageType === PAGE_TYPE.DOC &&
      docInitialData?.data?.path === path) {
      this.dealWithRes(docInitialData);
    } else {
      const result: any = await service.getDocumentDetail(params);
      this.helpResponseCode = result?.code;
      if (result?.code === 404 && !result?.success) {
        this.isNotFound = true;
      } else if (result?.data?.redirectUrl) {
        this.redirectUrl = result?.data?.redirectUrl;
      } else if (result?.data) {
        this.dealWithRes(result);
        this.helpDocVersion = result?.data?.version || 1;
      } else {
        sendSlsLog({ page: 'documentDetail', section: '', action: 'notFound', userParams1: JSON.stringify(result) });
      }
    }
  };

  /**
   * 获取文档动态数据
   * @param {object} 文档id: nodeId, 别名: alias
   * @returns
   */
  getDetailDynamicData = async ({ nodeId, alias }) => {
    const params = {
      nodeId,
      alias,
      website: WEBSITE,
      language: LANG,
    };
    const result: any = await service.getDetailDynamicData(params);
    return result?.data;
  };

  // 处理返回数据lists
  dealWithRes = (res) => {
    if (!res) return;
    // 将content中html标记对转义
    const escapedHtmlContent = escapeHtmlTag(res?.data?.content);
    let content = escapedHtmlContent;

    // ssr模式下数据不通过接口请求，在服务端渲染时添加中英文间距
    if (isServer()) {
      content = addInitContentSpace(res?.data, escapedHtmlContent);

      // const params = { history, data: res?.data, plugins: ssrPlugins };
      // ssrPlugins.forEach((item) => {
      //   const plugin = item as any;
      //   const [process, bindEvent] = plugin;
      //   if (process) {
      //     process(params);
      //   }
      //   if (bindEvent) {
      //     bindEvent(params);
      //   }
      // });
    }

    this.data = { ...res?.data, content };

    const { breadcrumb } = extractDocumentDetail(res?.data);
    this.breadcrumb = breadcrumb;

    // 判断是否需要展示警告NoteTip
    this.showNoteTip = /class="note (warning |note )?note-warning"/.test(res?.data?.content);

    // autoTranslation 为机器翻译的特殊标志
    this.isMachineTranslation = res?.data?.tags?.find((item) => item?.key === 'autoTranslation')?.value || false;
  };

  // 初始化detailModel
  initData = () => {
    this.data = {};
    this.breadcrumb = [];
    this.isMachineTranslation = false;
    this.showNoteTip = false;
  };
}
