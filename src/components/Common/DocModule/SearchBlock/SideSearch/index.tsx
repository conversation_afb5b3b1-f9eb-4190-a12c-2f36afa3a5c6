/* eslint-disable @typescript-eslint/no-shadow */
import React, { useState, useMemo, useEffect, useRef } from 'react';
import { useRequest } from 'ice';
import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { isEmpty, debounce } from 'lodash';
import UpdownArrow, { EArrowType } from '@/help-fe-common/components/common/UpdownArrow';
import SearchResult from '@/components/Common/DocModule/SearchBlock/SearchResult';
import SearchInput from '@/components/Common/DocModule/SearchBlock/SearchInput';
import Loading from '@/help-fe-common/components/common/Loading';
import services from '@/help-fe-common/services/search';
import { sendSlsLog } from '@/help-fe-common/utils/global/track/slsLogger';
import { onCancelClick } from '@/help-fe-common/utils/helpDoc/searchClick';
import { getProductInfo, deleteProductInfo, setLocalSearchValue, removeLocalSearchValue } from '@/help-fe-common/utils/helpDoc/searchStorage';
import styles from './index.module.scss';

interface IProps {
  keywords?: string;
  defaultFilter?: string;
  onSelectChange?: Function;
}

export enum ALLOWED_FILTER_ENUM {
  ALL = 'all',
  PRODUCT = 'product',
}

export default ({
  keywords,
  defaultFilter,
  onSelectChange,
}: IProps) => {
  const [param, setParam] = useState({});
  const [currentCategory, setCurrentCategory] = useState('');
  const [currentLevel, setCurrentLevel] = useState(3);// 默认层级为3表示产品，层级为4为子产品
  const [expandStatus, setExpandStatus] = useState(true);
  const [offset, setOffset] = useState(0);
  // 页面懒加载相关
  const [scrollListen, setScrollListen] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [resultData, setResultData] = useState<any>();
  const { data, request: getSearch, loading } = useRequest(services.getSearch, { manual: true });
  const [productInfo, setProductInfo] = useState<any>(getProductInfo());
  const sideSearchContainer = useRef<any>(null);

  /**
   * 搜索分类处理 1.product/practice维度需要添加'全部'分类; 2.product维度搜索，需要添加当前product分类
   * @param categories 分类数据
   * @param defaultFilter 默认搜索维度（即搜索打开入口，首页、文档页）
   * @returns
   */
  const handleCategoryData = (categories, defaultFilter) => {
    let newCategory = JSON.parse(JSON.stringify(categories));
    // 对分类数据单独处理
    if (productInfo
      && defaultFilter === ALLOWED_FILTER_ENUM.PRODUCT) {
      // 搜索维度为product将当前productId置顶
      newCategory = categories?.filter((item) => item?.id !== productInfo?.id);
      newCategory?.unshift(productInfo);
    }
    newCategory?.unshift({ id: '-1', name: '全部' });
    return newCategory;
  };

  const categoryData = useMemo(() => {
    if (data?.categories) {
      return handleCategoryData(data?.categories, defaultFilter);
    } else {
      return [];
    }
  }, [data?.categories]);

  const inputFocus = (e) => {
    e.preventDefault();
    const input = document.getElementById('top-search-input');
    input?.focus();
  };

  // 切换产品/领域分类
  const onCategoryClick = (e, item) => {
    const { id, level, name } = item;
    inputFocus(e);
    setCurrentCategory(id);
    setCurrentLevel(level);
    setScrollListen(true);
    setCurrentPage(1);
    sendSlsLog({
      page: '',
      action: 'click',
      section: 'suggestCategory',
      userParams1: productInfo?.name,
      userParams2: name,
      keywords: debounceParam?.keywords,
    });
  };

  const onRemove = () => {
    if (defaultFilter === ALLOWED_FILTER_ENUM.ALL) {
      // 首页主搜形态下，清除缓存，避免在其他页面中sideSearch默认打开
      deleteProductInfo();
    }
    removeLocalSearchValue();
    onSelectChange && onSelectChange();
  };

  // 点击取消sideSearch显示
  const onClickListener = (e) => {
    onCancelClick(e, 'help-side-search', onRemove);
  };

  // input输入防抖
  const useDebounce = (param, delay) => {
    const [value, setValue] = useState(param);
    useEffect(() => {
      const timer = setTimeout(() => setValue(param), delay);
      return () => clearTimeout(timer);
    }, [param, delay]);
    return value;
  };
  const debounceParam = useDebounce(param, 200);

  // 处理input数据
  const changeParam = (param) => {
    setParam(param);
  };

  const onScroll = () => {
    // 判断practiceList区域是否滚动到底部
    const listContainer = document.getElementById('help-sideSearch-container');
    const scrollTop = listContainer?.scrollTop as number;
    const clientHeight = listContainer?.clientHeight as number;
    const scrollHeight = listContainer?.scrollHeight as number;
    // 底部距离200px请求下一页
    if (scrollTop + clientHeight + 200 >= scrollHeight) {
      setCurrentPage(currentPage + 1);
    }
  };

  const debounceScroll = debounce(() => onScroll(), 100);

  // 初始化搜索分类
  useEffect(() => {
    const localProductInfo = getProductInfo();
    if (defaultFilter === ALLOWED_FILTER_ENUM.PRODUCT && localProductInfo?.id) {
      setProductInfo(localProductInfo);
      setCurrentCategory(localProductInfo?.id);
      setCurrentLevel(localProductInfo?.level);
    } else {
      setCurrentCategory('-1');
    }
  }, [defaultFilter]);

  // 首页点击侧边搜索特殊逻辑
  useEffect(() => {
    if (!(currentCategory && defaultFilter === ALLOWED_FILTER_ENUM.ALL)) return;
    // 分类选中全部则清除localStorage
    if (currentCategory === '-1') {
      deleteProductInfo();
    } else {
      const param = categoryData?.find((item) => item?.id === currentCategory);
      window.localStorage.setItem('productInfo', JSON.stringify(param));
    }
  }, [defaultFilter, currentCategory]);

  useEffect(() => {
    // 重置scrollListen状态，避免输入中的无结果影响
    setScrollListen(true);
    if (!isEmpty(debounceParam)) {
      const listContainer = document.getElementById('help-sideSearch-container');
      listContainer && (listContainer.scrollTop = 0);
      const pageSize = debounceParam?.keywords ? 20 : 10;
      getSearch({ ...debounceParam, pageNum: 1, pageSize }).then((data) => {
        if (data?.documents?.data?.length || data?.products?.data?.length) {
          setResultData(data);
        } else {
          setScrollListen(false);
          setResultData({});
        }
      });
      sendSlsLog({ page: '', section: 'sideSearch', userParams1: 'searchValue', userParams2: debounceParam?.keywords });
      setLocalSearchValue(debounceParam?.keywords, defaultFilter);
    }
  }, [debounceParam]);

  useEffect(() => {
    if (data) {
      // offset有值时，需要加上categoryContainer的marginTop 16px；否则默认64px
      const value = sideSearchContainer?.current?.offsetTop ? Number(sideSearchContainer?.current?.offsetTop) + 80 : 64;
      setOffset(value);
    }
  }, [data, expandStatus]);

  useEffect(() => {
    if (currentPage && currentPage !== 1) {
      getSearch({ ...debounceParam, pageNum: currentPage, pageSize: 20 }).then((data) => {
        if (data?.documents?.data?.length || data?.products?.data?.length) {
          const { documents, products, categories, keywords } = data;
          const { documents: oldData } = resultData;
          setResultData({ products, categories, documents: { data: [...oldData?.data, ...documents?.data] }, keywords });
        } else {
          setScrollListen(false);
        }
      });
    }
  }, [currentPage]);

  useEffect(() => {
    window.addEventListener('click', onClickListener);
    return () => {
      window.removeEventListener('click', onClickListener);
    };
  }, []);

  return (
    <div className={classnames(styles.helpSideSearch, 'help-side-search')}>
      <div className={styles.cancelButton}>
        <i
          className={classnames(['smallFont', 'normalFont'], 'help-iconfont help-icon-delete')}
          onClick={() => { onRemove(); }}
        />
      </div>
      <div className={styles.head}>
        <SearchInput
          keywords={keywords}
          allowPop={false}
          currentFilter={defaultFilter}
          categoryId={currentCategory}
          level={currentLevel}
          onChange={(param) => changeParam(param)}
          onPop={() => { }}
        />
      </div>
      <div className={styles.mainContainer}>
        {
          categoryData?.length > 0 ?
            <div className={styles.categoryContainer}>
              <div className={styles.title}>
                <span>
                  <FormattedMessage id={'help.search.category1'} />
                </span>
                <span
                  style={{ display: categoryData?.length > 4 ? 'inline-block' : 'none' }}
                  onClick={() => { setExpandStatus(!expandStatus); }}
                >
                  <UpdownArrow type={expandStatus ? EArrowType.UP : EArrowType.DOWN} />
                </span>
              </div>
              <div className={styles.content} style={expandStatus ? { height: 'auto' } : { height: '24px', overflow: 'hidden' }}>
                {
                  categoryData?.map((item, index) => (
                    <span
                      className={classnames(styles.categoryItem,
                        currentCategory === item?.id ? styles.highlight : '')}
                      key={index}
                      onClick={(e) => { onCategoryClick(e, item); }}
                    >{item?.name}
                    </span>
                  ))
                }
              </div>
            </div> :
            null
        }
        <div
          className={styles.suggestContainer}
          id="help-sideSearch-container"
          ref={sideSearchContainer}
          onScrollCapture={scrollListen ? debounceScroll : () => { }}
          style={{ height: `calc(100vh - ${offset}px)` }}
        >
          <Loading loading={loading} style={{}} />
          {
            resultData ?
              <>
                {
                  resultData?.products?.data?.length || resultData?.documents?.data?.length ?
                    <>
                      {
                        data?.documents?.totalCount > 0 ?
                          <div className={styles.resultCount}>
                            {currentCategory === productInfo?.id && <FormattedMessage id={'help.search.countTip'} />}
                            <FormattedMessage id={'help.search.countTip1'} />
                            {Number(data?.documents?.totalCount) > 9999 ? '9999+' : data?.documents?.totalCount}
                            <FormattedMessage id={'help.search.countTip2'} />
                          </div> :
                          null
                      }
                      <SearchResult
                        data={resultData}
                        onSelectChange={onSelectChange}
                      />
                    </>
                    :
                    <div className={styles.suggestTip}>
                      <i className="help-iconfont help-icon-zhuyi" />
                      <FormattedMessage id="help.search.noResult" />
                    </div>
                }
              </> :
              null
          }
        </div>
      </div>
    </div >
  );
};
